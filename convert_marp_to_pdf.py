#!/usr/bin/env python3
"""
Convert Marp Markdown slides to PDF
"""

import subprocess
import sys
import os

def check_marp_installed():
    """Check if Marp CLI is installed"""
    try:
        result = subprocess.run(['marp', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Marp CLI found: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_marp():
    """Install Marp CLI using npm"""
    print("📦 Installing Marp CLI...")
    try:
        # Check if npm is available
        subprocess.run(['npm', '--version'], 
                      capture_output=True, check=True)
        
        # Install Marp CLI globally
        result = subprocess.run(['npm', 'install', '-g', '@marp-team/marp-cli'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Marp CLI installed successfully!")
            return True
        else:
            print(f"❌ Failed to install Marp CLI: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ npm not found. Please install Node.js first.")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing Marp CLI: {e}")
        return False

def convert_to_pdf():
    """Convert Marp markdown to PDF"""
    markdown_file = "python_functions_slides.md"
    pdf_file = "Python_Functions_Slides.pdf"
    
    if not os.path.exists(markdown_file):
        print(f"❌ Error: {markdown_file} not found!")
        return False
    
    try:
        print("🔄 Converting Markdown to PDF...")
        result = subprocess.run([
            'marp', 
            markdown_file,
            '--pdf',
            '--output', pdf_file,
            '--theme', 'default',
            '--allow-local-files'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ PDF created successfully: {pdf_file}")
            return True
        else:
            print(f"❌ Error converting to PDF: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function"""
    print("🎯 Marp Markdown to PDF Converter")
    print("=" * 40)
    
    # Check if Marp is installed
    if not check_marp_installed():
        print("⚠️  Marp CLI not found. Attempting to install...")
        if not install_marp():
            print("\n📋 Manual installation instructions:")
            print("1. Install Node.js from https://nodejs.org/")
            print("2. Run: npm install -g @marp-team/marp-cli")
            print("3. Run this script again")
            return False
    
    # Convert to PDF
    if convert_to_pdf():
        print("\n🎉 Conversion completed successfully!")
        print("\n📖 Alternative methods if PDF generation fails:")
        print("1. Install Marp for VS Code extension")
        print("2. Open python_functions_slides.md in VS Code")
        print("3. Use Command Palette: 'Marp: Export Slide Deck'")
        print("4. Choose PDF format")
        return True
    else:
        print("\n📖 Alternative methods:")
        print("1. Use Marp CLI directly: marp python_functions_slides.md --pdf")
        print("2. Use online Marp editor: https://web.marp.app/")
        print("3. Use VS Code with Marp extension")
        return False

if __name__ == "__main__":
    main()
