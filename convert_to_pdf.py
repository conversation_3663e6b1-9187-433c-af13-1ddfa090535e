#!/usr/bin/env python3
"""
Convert HTML slides to PDF using weasyprint
"""

try:
    from weasyprint import HTML, CSS
    import os
    
    def convert_slides_to_pdf():
        """Convert the HTML slides to PDF"""
        
        # Check if HTML file exists
        html_file = "python_functions_course.html"
        if not os.path.exists(html_file):
            print(f"Error: {html_file} not found!")
            return False

        try:
            # Convert HTML to PDF
            print("Converting HTML slides to PDF...")
            HTML(filename=html_file).write_pdf("Python_Functions_Course_Slides.pdf")
            print("✅ PDF created successfully: Python_Functions_Course_Slides.pdf")
            return True
            
        except Exception as e:
            print(f"❌ Error converting to PDF: {e}")
            return False
    
    if __name__ == "__main__":
        convert_slides_to_pdf()

except ImportError:
    print("WeasyPrint not installed. Installing...")
    import subprocess
    import sys
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "weasyprint"])
        print("WeasyPrint installed successfully!")
        
        # Try again after installation
        from weasyprint import HTML, CSS
        
        def convert_slides_to_pdf():
            """Convert the HTML slides to PDF"""
            
            # Check if HTML file exists
            html_file = "python_functions_course.html"
            if not os.path.exists(html_file):
                print(f"Error: {html_file} not found!")
                return False

            try:
                # Convert HTML to PDF
                print("Converting HTML slides to PDF...")
                HTML(filename=html_file).write_pdf("Python_Functions_Course_Slides.pdf")
                print("✅ PDF created successfully: Python_Functions_Course_Slides.pdf")
                return True
                
            except Exception as e:
                print(f"❌ Error converting to PDF: {e}")
                return False
        
        if __name__ == "__main__":
            convert_slides_to_pdf()
            
    except Exception as e:
        print(f"❌ Failed to install WeasyPrint: {e}")
        print("\nAlternative methods to create PDF:")
        print("1. Open python_functions_course.html in your browser")
        print("2. Use browser's Print function")
        print("3. Select 'Save as PDF' or 'Print to PDF'")
        print("4. Adjust settings for best results")
