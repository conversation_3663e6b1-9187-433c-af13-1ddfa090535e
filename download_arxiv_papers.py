#!/usr/bin/env python3
"""
Script to download recent Information Retrieval papers from arXiv
"""

import requests
import os
import time
from urllib.parse import urlparse

# Create directory for papers
os.makedirs('arxiv_papers', exist_ok=True)

# List of recent Information Retrieval papers from arXiv
papers = [
    {
        'id': '2412.13102',
        'title': 'AIR-Bench: Automated Heterogeneous Information Retrieval Benchmark',
        'date': '2024-12-17'
    },
    {
        'id': '2406.01273',
        'title': 'SoccerRAG: Multimodal Soccer Information Retrieval via Natural Language Queries',
        'date': '2024-06-03'
    },
    {
        'id': '2404.14851',
        'title': 'From Matching to Generation: A Survey on Generative Information Retrieval',
        'date': '2024-04-23'
    },
    {
        'id': '2404.11457',
        'title': 'Bias and Unfairness in Information Retrieval Systems: New Challenges in the LLM Era',
        'date': '2024-04-17'
    },
    {
        'id': '2402.16200',
        'title': 'IR2: Information Regularization for Information Retrieval',
        'date': '2024-02-25'
    },
    {
        'id': '2402.11203',
        'title': 'Exploring ChatGPT for Next-generation Information Retrieval',
        'date': '2024-02-17'
    },
    {
        'id': '2312.10997',
        'title': 'Retrieval-Augmented Generation for Large Language Models: A Survey',
        'date': '2023-12-18'
    },
    {
        'id': '2310.07581',
        'title': 'Qlarify: Recursively Expandable Abstracts for Directed Information Retrieval over Scientific Papers',
        'date': '2023-10-11'
    },
    {
        'id': '2308.07107',
        'title': 'Large Language Models for Information Retrieval: A Survey',
        'date': '2023-08-14'
    },
    {
        'id': '2307.09288',
        'title': 'Dense Passage Retrieval for Open-Domain Question Answering',
        'date': '2023-07-18'
    }
]

def download_paper(paper_id, title, date):
    """Download a paper from arXiv"""
    url = f"https://arxiv.org/pdf/{paper_id}.pdf"
    
    # Clean title for filename
    clean_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
    clean_title = clean_title.replace(' ', '_')[:50]  # Limit length
    
    filename = f"arxiv_papers/{paper_id}_{clean_title}.pdf"
    
    print(f"Downloading: {title}")
    print(f"URL: {url}")
    print(f"Saving to: {filename}")
    
    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        file_size = os.path.getsize(filename)
        print(f"✓ Downloaded successfully ({file_size:,} bytes)")
        return True
        
    except Exception as e:
        print(f"✗ Failed to download: {e}")
        return False

def main():
    print("Downloading 10 latest Information Retrieval papers from arXiv...")
    print("=" * 60)
    
    successful_downloads = 0
    
    for i, paper in enumerate(papers, 1):
        print(f"\n[{i}/10] {paper['date']}")
        
        if download_paper(paper['id'], paper['title'], paper['date']):
            successful_downloads += 1
        
        # Add a small delay between downloads to be respectful
        if i < len(papers):
            time.sleep(2)
    
    print("\n" + "=" * 60)
    print(f"Download complete! {successful_downloads}/{len(papers)} papers downloaded successfully.")
    print(f"Papers saved in: {os.path.abspath('arxiv_papers')}")

if __name__ == "__main__":
    main()
