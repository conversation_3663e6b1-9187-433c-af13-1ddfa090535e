---
marp: true
theme: default
class: lead
paginate: true
backgroundColor: #fff
backgroundImage: url('https://marp.app/assets/hero-background.svg')
header: 'Python Functions - University CS Course'
footer: '© 2024 Computer Science Department'
---

<style>
.hljs-keyword { color: #d73a49; font-weight: bold; }
.hljs-string { color: #032f62; }
.hljs-function { color: #6f42c1; }
.hljs-number { color: #005cc5; }
.hljs-comment { color: #6a737d; font-style: italic; }
.hljs-built_in { color: #e36209; }

h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
h3 { color: #7f8c8d; }

.highlight { background: #f39c12; color: white; padding: 5px 10px; border-radius: 5px; }
.tip { background: #e8f5e8; border-left: 5px solid #27ae60; padding: 15px; margin: 10px 0; }
.warning { background: #ffe6e6; border-left: 5px solid #e74c3c; padding: 15px; margin: 10px 0; }
.example { background: #ecf0f1; border-left: 5px solid #3498db; padding: 15px; margin: 10px 0; }

pre { background: #2d3748; border-radius: 8px; padding: 20px; }
code { font-family: 'Fira Code', 'Consolas', monospace; }
</style>

# Python Functions
## 构建模块化编程的基础

**计算机科学课程 - 45分钟**
**大学级别**

---

## 课程大纲

- **函数基础** - 什么是函数？
- **函数语法** - 基本结构
- **参数传递** - 数据输入
- **返回值** - 结果输出
- **作用域** - 变量范围
- **高级特性** - 默认参数、可变参数
- **最佳实践** - 编写优雅函数
- **实际应用** - 项目案例

---

## 什么是函数？

### 函数是一个**可重用的代码块**，用于执行特定的任务

<div class="example">

**🔧 就像现实中的工具**
输入 → 处理 → 输出

</div>

---

## 为什么使用函数？

| 优势 | 说明 |
|------|------|
| 🔄 **可重用性** | 写一次，用多次 |
| 📦 **模块化** | 将复杂问题分解 |
| 🧹 **代码整洁** | 保持代码有序 |
| 🐛 **易于调试** | 隔离和修复问题 |

---

## 基本函数语法

```python
def function_name(parameters):
    """
    函数文档字符串（可选）
    描述函数的功能
    """
    # 函数体 - 执行的代码
    
    return result  # 返回值（可选）
```

---

## 函数组成部分

- **`def`** - 定义函数的关键字
- **`function_name`** - 函数名称（遵循命名规范）
- **`parameters`** - 参数列表（可选）
- **`docstring`** - 文档字符串（可选但推荐）
- **`return`** - 返回语句（可选）

---

## 第一个函数

```python
def say_hello():
    """
    打印问候语
    """
    print("Hello, Python!")
    print("欢迎学习函数！")

# 调用函数
say_hello()
```

<div class="tip">

**💡 重要提示：** 函数必须先定义，后调用！

</div>

---

## 函数输出

**运行结果：**
```
Hello, Python!
欢迎学习函数！
```

---

## 带参数的函数

```python
def greet_person(name):
    """
    向特定的人问候
    """
    print(f"你好, {name}!")
    print("很高兴认识你！")

# 函数调用
greet_person("小明")
greet_person("小红")
```

---

## 多个参数

```python
def introduce_person(name, age, city):
    """
    介绍一个人的详细信息
    """
    print(f"大家好，我是{name}")
    print(f"我今年{age}岁")
    print(f"我来自{city}")

# 调用示例
introduce_person("张三", 20, "北京")
```

---

## 参数 vs 实参

### 参数 (Parameters)
函数定义时的变量
```python
def func(name, age):  # name, age 是参数
    pass
```

### 实参 (Arguments)
调用函数时传入的值
```python
func("小明", 18)  # "小明", 18 是实参
```

---

## 返回值

```python
def add_numbers(a, b):
    """
    计算两个数的和
    """
    result = a + b
    return result

# 使用返回值
sum_result = add_numbers(5, 3)
print(f"结果是: {sum_result}")  # 输出: 结果是: 8
```

---

## 多个返回值

```python
def get_name_parts(full_name):
    """
    分割姓名
    """
    parts = full_name.split()
    first_name = parts[0]
    last_name = parts[-1]
    return first_name, last_name

# 接收多个返回值
first, last = get_name_parts("张 三")
print(f"姓: {first}, 名: {last}")
```

---

## 变量作用域 - 全局变量

```python
# 全局变量
global_var = "我是全局变量"

def my_function():
    print(f"函数内部: {global_var}")

my_function()  # 可以访问全局变量
print(f"函数外部: {global_var}")
```

**输出：**
```
函数内部: 我是全局变量
函数外部: 我是全局变量
```

---

## 变量作用域 - 局部变量

```python
def my_function():
    local_var = "我是局部变量"  # 局部变量
    print(f"函数内部: {local_var}")

my_function()
# print(local_var)  # 错误！无法访问局部变量
```

<div class="warning">

**⚠️ 注意：** 局部变量只能在函数内部使用！

</div>

---

## 修改全局变量

```python
counter = 0  # 全局变量

def increment():
    global counter  # 声明使用全局变量
    counter += 1
    print(f"计数器: {counter}")

increment()  # 计数器: 1
increment()  # 计数器: 2
```

<div class="tip">

**💡 最佳实践：** 尽量避免使用全局变量，通过参数传递数据

</div>

---

## 默认参数

```python
def greet_with_title(name, title="同学"):
    """
    带称谓的问候
    """
    print(f"你好, {title} {name}!")

# 不同的调用方式
greet_with_title("小明")           # 使用默认值
greet_with_title("小红", "老师")    # 自定义称谓
```

**输出：**
```
你好, 同学 小明!
你好, 老师 小红!
```

---

## 多个默认参数

```python
def create_profile(name, age=18, city="未知"):
    """
    创建用户档案
    """
    return {
        'name': name,
        'age': age,
        'city': city
    }

# 灵活调用
profile1 = create_profile("小明")
profile2 = create_profile("小红", 20)
profile3 = create_profile("小李", city="上海")
```

---

## *args - 可变参数

```python
def sum_all(*args):
    """
    计算任意数量参数的和
    """
    total = 0
    for num in args:
        total += num
    return total

# 灵活调用
print(sum_all(1, 2, 3))        # 6
print(sum_all(1, 2, 3, 4, 5))  # 15
print(sum_all(10))             # 10
```

---

## **kwargs - 关键字参数

```python
def create_student(**kwargs):
    """
    创建学生信息
    """
    student = {}
    for key, value in kwargs.items():
        student[key] = value
    return student

# 使用示例
student = create_student(
    name="小明", 
    age=20, 
    major="计算机科学",
    gpa=3.8
)
```

---

## Lambda 函数

### Lambda = **匿名函数**

```python
# 普通函数
def square(x):
    return x ** 2

# Lambda 等价写法
square_lambda = lambda x: x ** 2

print(square(5))        # 25
print(square_lambda(5)) # 25
```

---

## Lambda 与内置函数

```python
numbers = [1, 2, 3, 4, 5]

# 使用 map()
squared = list(map(lambda x: x**2, numbers))
print(squared)  # [1, 4, 9, 16, 25]

# 使用 filter()
even = list(filter(lambda x: x % 2 == 0, numbers))
print(even)     # [2, 4]
```

<div class="tip">

**💡 适用场景：** 简单的一行表达式

</div>

---

## 最佳实践 - 函数命名

### ✅ 好的函数名
```python
def calculate_total_price():
    pass

def is_valid_email():
    pass

def get_user_input():
    pass
```

### ❌ 不好的函数名
```python
def calc():      # 太短
    pass

def doStuff():   # 不清楚
    pass
```

---

## 函数文档

```python
def calculate_interest(principal, rate, time):
    """
    计算复利
    
    参数:
        principal (float): 本金
        rate (float): 年利率 (小数形式)
        time (int): 年数
    
    返回:
        float: 最终金额
    
    示例:
        >>> calculate_interest(1000, 0.05, 2)
        1102.5
    """
    amount = principal * (1 + rate) ** time
    return round(amount, 2)
```

---

## 总结与练习

### 🎯 **练习题：**

1. **编写一个判断质数的函数**
2. **创建一个字符串反转函数**
3. **设计一个计算列表最大值的函数**
4. **构建一个统计词频的函数**

### 📚 **关键概念回顾：**
- 函数定义与调用
- 参数传递与返回值
- 作用域管理
- 高级特性应用

---

# 谢谢！
## Questions & Discussion

**联系方式：**
- 📧 Email: <EMAIL>
- 💬 Office Hours: 周二、周四 2-4 PM
- 📚 课程资料: course.university.edu
