
import re
from bs4 import BeautifulSoup

def classify_url(url, title):
    """Classifies a URL into a category based on keywords."""
    if re.search(r'github|stackoverflow|python|javascript', url) or re.search(r'programming|coding|develop', title, re.IGNORECASE):
        return "Programming"
    elif re.search(r'news|bbc|cnn|reuters', url) or re.search(r'news|article', title, re.IGNORECASE):
        return "News"
    elif re.search(r'amazon|ebay|shopping', url) or re.search(r'shop|buy|store', title, re.IGNORECASE):
        return "Shopping"
    elif re.search(r'twitter|facebook|linkedin', url) or re.search(r'social|tweet', title, re.IGNORECASE):
        return "Social"
    else:
        return "Other"

def reclassify_bookmarks(input_file, output_file):
    """Reads bookmarks from an HTML file, reclassifies them, and writes them to a new HTML file."""
    with open(input_file, 'r') as f:
        soup = BeautifulSoup(f, 'html.parser')

    bookmarks = {}
    for a in soup.find_all('a'):
        url = a.get('href')
        title = a.string
        if url and title:
            category = classify_url(url, title)
            if category not in bookmarks:
                bookmarks[category] = []
            bookmarks[category].append((url, title))

    # Create the new HTML structure
    new_soup = BeautifulSoup('''<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
</DL><p>''', 'html.parser')
    dl = new_soup.find('dl')

    for category, items in bookmarks.items():
        dt = new_soup.new_tag('dt')
        h3 = new_soup.new_tag('h3')
        h3.string = category
        dt.append(h3)
        dl.append(dt)

        category_dl = new_soup.new_tag('dl')
        p = new_soup.new_tag('p')
        category_dl.append(p)
        dt.append(category_dl)

        for url, title in items:
            item_dt = new_soup.new_tag('dt')
            item_a = new_soup.new_tag('a', href=url)
            item_a.string = title
            item_dt.append(item_a)
            category_dl.append(item_dt)

    with open(output_file, 'w') as f:
        f.write(str(new_soup))

if __name__ == '__main__':
    reclassify_bookmarks('bookmarks_2025_6_30.html', 'bookmarks_1.html')
