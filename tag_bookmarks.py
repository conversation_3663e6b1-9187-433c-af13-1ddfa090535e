
import re
from bs4 import BeautifulSoup
import json

def generate_tag(url, title):
    """Generates a tag based on keywords in the URL and title."""
    url_lower = url.lower()
    title_lower = title.lower()

    if "google scholar" in title_lower or "scholar.google.com" in url_lower:
        return "Academic Search"
    elif "google.com" in url_lower and "search" in title_lower:
        return "Search Engine"
    elif "gmail" in title_lower or "accounts.google.com" in url_lower:
        return "Email"
    elif "openrouter" in url_lower:
        return "AI API"
    elif "miaoz" in url_lower:
        return "Dashboard"
    elif "perplexity" in url_lower:
        return "AI Search"
    elif "chat.openai.com" in url_lower:
        return "AI Chatbot"
    elif "gemini.google.com" in url_lower:
        return "AI Assistant"
    elif "poe.com" in url_lower:
        return "AI Platform"
    elif "sigir-2024.github.io" in url_lower:
        return "Academic Conference"
    elif "github.com" in url_lower or "programming" in title_lower:
        return "Programming"
    elif "news" in url_lower or "news" in title_lower:
        return "News"
    elif "shopping" in url_lower or "shop" in title_lower:
        return "Shopping"
    elif "social" in url_lower or "facebook" in url_lower or "twitter" in url_lower:
        return "Social Media"
    else:
        return "General"

def extract_bookmarks(input_file, limit=None):
    """Reads a bookmark HTML file and extracts the links, titles, and generates tags."""
    with open(input_file, 'r') as f:
        soup = BeautifulSoup(f, 'html.parser')

    bookmarks = []
    count = 0
    for a in soup.find_all('a'):
        url = a.get('href')
        title = a.string
        if url and title:
            tag = generate_tag(url, title)
            bookmarks.append({'url': url, 'title': title, 'tag': tag})
            count += 1
            if limit is not None and count >= limit:
                break
    
    return bookmarks

def write_bookmarks_to_html(bookmarks, output_file):
    """Writes the bookmarks and their tags to an HTML file."""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Tagged Bookmarks</title>
    <style>
        body { font-family: sans-serif; }
        .bookmark-item { margin-bottom: 10px; border: 1px solid #ccc; padding: 10px; }
        .bookmark-title { font-weight: bold; }
        .bookmark-url { color: #0066cc; text-decoration: none; }
        .bookmark-tag { font-style: italic; color: #666; }
    </style>
</head>
<body>
    <h1>Tagged Bookmarks</h1>
    <div>
"""

    for bookmark in bookmarks:
        html_content += f"""
        <div class="bookmark-item">
            <div class="bookmark-title"><a href="{bookmark['url']}" class="bookmark-url">{bookmark['title']}</a></div>
            <div class="bookmark-tag">Tag: {bookmark['tag']}</div>
        </div>
"""

    html_content += """
    </div>
</body>
</html>
"""

    with open(output_file, 'w') as f:
        f.write(html_content)

def main():
    bookmarks = extract_bookmarks('bookmarks_2025_6_30.html', limit=200)
    write_bookmarks_to_html(bookmarks, 'tets.html')

if __name__ == '__main__':
    main()
