import pygame
import math
import random
import sys

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
DARK_GREEN = (0, 100, 0)
ORANGE = (255, 165, 0)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Tank Fight Game")
        self.clock = pygame.time.Clock()
        self.running = True
        self.game_over = False
        self.winner = None

        # Game objects
        self.tanks = []
        self.bullets = []
        self.explosions = []  # For explosion effects

        # Scoring system
        self.scores = {"Player 1": 0, "Player 2": 0}
        self.round_number = 1

        # Initialize tanks
        self.init_tanks()
        
    def init_tanks(self):
        """Initialize the two player tanks"""
        # Player 1 tank (Red) - starts on the left
        tank1 = Tank(100, SCREEN_HEIGHT // 2, RED, "Player 1")
        tank1.controls = {
            'forward': pygame.K_w,
            'backward': pygame.K_s,
            'turn_left': pygame.K_a,
            'turn_right': pygame.K_d,
            'turret_left': pygame.K_q,
            'turret_right': pygame.K_e,
            'shoot': pygame.K_SPACE
        }

        # Player 2 tank (Blue) - starts on the right
        tank2 = Tank(SCREEN_WIDTH - 100, SCREEN_HEIGHT // 2, BLUE, "Player 2")
        tank2.controls = {
            'forward': pygame.K_UP,
            'backward': pygame.K_DOWN,
            'turn_left': pygame.K_LEFT,
            'turn_right': pygame.K_RIGHT,
            'turret_left': pygame.K_COMMA,
            'turret_right': pygame.K_PERIOD,
            'shoot': pygame.K_RETURN
        }
        
        self.tanks = [tank1, tank2]
    
    def handle_events(self):
        """Handle all game events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r and self.game_over:
                    self.restart_game()
                elif event.key == pygame.K_ESCAPE:
                    self.running = False
    
    def update(self):
        """Update game state"""
        if not self.game_over:
            # Update tanks
            for tank in self.tanks:
                if tank.alive:
                    tank.update(self.bullets)

            # Update bullets
            for bullet in self.bullets[:]:
                bullet.update()
                # Remove bullets that are off screen
                if (bullet.x < 0 or bullet.x > SCREEN_WIDTH or
                    bullet.y < 0 or bullet.y > SCREEN_HEIGHT):
                    self.bullets.remove(bullet)

            # Update explosions
            for explosion in self.explosions[:]:
                explosion.update()
                if explosion.finished:
                    self.explosions.remove(explosion)

            # Check for collisions
            self.check_collisions()

            # Check for game over
            self.check_game_over()
    
    def check_collisions(self):
        """Check for all types of collisions"""
        # Bullet-tank collisions
        for bullet in self.bullets[:]:
            for tank in self.tanks:
                if tank.alive and tank != bullet.owner:
                    if self.bullet_tank_collision(bullet, tank):
                        # Create explosion effect
                        self.create_explosion(bullet.x, bullet.y)
                        tank.take_damage(bullet.damage)
                        self.bullets.remove(bullet)
                        break

        # Tank-tank collisions
        for i, tank1 in enumerate(self.tanks):
            for tank2 in self.tanks[i+1:]:
                if tank1.alive and tank2.alive:
                    if self.tank_tank_collision(tank1, tank2):
                        self.resolve_tank_collision(tank1, tank2)

    def bullet_tank_collision(self, bullet, tank):
        """Check if bullet collides with tank using more accurate collision detection"""
        # Simple circular collision for now
        distance = math.sqrt((bullet.x - tank.x)**2 + (bullet.y - tank.y)**2)
        return distance < (tank.size + bullet.size)

    def tank_tank_collision(self, tank1, tank2):
        """Check if two tanks collide"""
        distance = math.sqrt((tank1.x - tank2.x)**2 + (tank1.y - tank2.y)**2)
        return distance < (tank1.size + tank2.size)

    def resolve_tank_collision(self, tank1, tank2):
        """Resolve collision between two tanks by pushing them apart"""
        # Calculate direction vector
        dx = tank2.x - tank1.x
        dy = tank2.y - tank1.y
        distance = math.sqrt(dx**2 + dy**2)

        if distance > 0:
            # Normalize direction vector
            dx /= distance
            dy /= distance

            # Calculate overlap
            overlap = (tank1.size + tank2.size) - distance

            # Push tanks apart
            push_distance = overlap / 2
            tank1.x -= dx * push_distance
            tank1.y -= dy * push_distance
            tank2.x += dx * push_distance
            tank2.y += dy * push_distance

            # Keep tanks within bounds
            tank1.x = max(tank1.size, min(SCREEN_WIDTH - tank1.size, tank1.x))
            tank1.y = max(tank1.size, min(SCREEN_HEIGHT - tank1.size, tank1.y))
            tank2.x = max(tank2.size, min(SCREEN_WIDTH - tank2.size, tank2.x))
            tank2.y = max(tank2.size, min(SCREEN_HEIGHT - tank2.size, tank2.y))

    def create_explosion(self, x, y):
        """Create explosion effect at given position"""
        explosion = Explosion(x, y)
        self.explosions.append(explosion)
    
    def check_game_over(self):
        """Check if game is over"""
        alive_tanks = [tank for tank in self.tanks if tank.alive]
        if len(alive_tanks) <= 1:
            self.game_over = True
            if len(alive_tanks) == 1:
                self.winner = alive_tanks[0]
                # Update score
                self.scores[self.winner.name] += 1
            else:
                self.winner = None  # Draw
    
    def restart_game(self):
        """Restart the game"""
        self.game_over = False
        self.winner = None
        self.bullets.clear()
        self.explosions.clear()
        self.round_number += 1
        self.init_tanks()
    
    def draw(self):
        """Draw everything on screen"""
        self.screen.fill(DARK_GREEN)

        # Draw background pattern
        self.draw_background()

        # Draw tanks
        for tank in self.tanks:
            tank.draw(self.screen)

        # Draw bullets
        for bullet in self.bullets:
            bullet.draw(self.screen)

        # Draw explosions
        for explosion in self.explosions:
            explosion.draw(self.screen)

        # Draw UI
        self.draw_ui()

        # Draw game over screen
        if self.game_over:
            self.draw_game_over()

        pygame.display.flip()

    def draw_background(self):
        """Draw background pattern"""
        # Draw grid pattern
        grid_size = 50
        for x in range(0, SCREEN_WIDTH, grid_size):
            pygame.draw.line(self.screen, (0, 80, 0), (x, 0), (x, SCREEN_HEIGHT), 1)
        for y in range(0, SCREEN_HEIGHT, grid_size):
            pygame.draw.line(self.screen, (0, 80, 0), (0, y), (SCREEN_WIDTH, y), 1)
    
    def draw_ui(self):
        """Draw user interface elements"""
        font = pygame.font.Font(None, 36)
        small_font = pygame.font.Font(None, 24)

        # Draw health bars
        for i, tank in enumerate(self.tanks):
            y_pos = 20 + i * 50
            # Health bar background
            pygame.draw.rect(self.screen, RED, (20, y_pos, 200, 20))
            # Health bar foreground
            health_width = int((tank.health / tank.max_health) * 200)
            pygame.draw.rect(self.screen, GREEN, (20, y_pos, health_width, 20))
            # Health bar border
            pygame.draw.rect(self.screen, WHITE, (20, y_pos, 200, 20), 2)
            # Player name
            text = font.render(tank.name, True, WHITE)
            self.screen.blit(text, (230, y_pos - 5))
            # Health percentage
            health_text = small_font.render(f"{tank.health}/{tank.max_health}", True, WHITE)
            self.screen.blit(health_text, (25, y_pos + 25))

        # Draw scores
        score_y = 130
        score_text = font.render("SCORES", True, WHITE)
        self.screen.blit(score_text, (20, score_y))

        for i, (player, score) in enumerate(self.scores.items()):
            y_pos = score_y + 35 + i * 30
            score_display = font.render(f"{player}: {score}", True, WHITE)
            self.screen.blit(score_display, (20, y_pos))

        # Draw round number
        round_text = font.render(f"Round {self.round_number}", True, WHITE)
        round_rect = round_text.get_rect(center=(SCREEN_WIDTH // 2, 30))
        self.screen.blit(round_text, round_rect)

        # Draw controls help
        help_y = SCREEN_HEIGHT - 120
        help_font = pygame.font.Font(None, 18)
        controls = [
            "Player 1: W/S=move, A/D=turn tank, Q/E=turn turret, SPACE=shoot",
            "Player 2: ↑/↓=move, ←/→=turn tank, ,/.=turn turret, ENTER=shoot",
            "Press R to restart, ESC to quit"
        ]
        for i, control in enumerate(controls):
            help_text = help_font.render(control, True, WHITE)
            self.screen.blit(help_text, (20, help_y + i * 20))
    
    def draw_game_over(self):
        """Draw game over screen"""
        # Semi-transparent overlay
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        font_large = pygame.font.Font(None, 72)
        font_small = pygame.font.Font(None, 36)
        
        if self.winner:
            text = font_large.render(f"{self.winner.name} Wins!", True, WHITE)
        else:
            text = font_large.render("Draw!", True, WHITE)
        
        text_rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 50))
        self.screen.blit(text, text_rect)
        
        restart_text = font_small.render("Press R to restart or ESC to quit", True, WHITE)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 50))
        self.screen.blit(restart_text, restart_rect)
    
    def run(self):
        """Main game loop"""
        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(FPS)
        
        pygame.quit()
        sys.exit()

class Tank:
    def __init__(self, x, y, color, name):
        self.x = x
        self.y = y
        self.color = color
        self.name = name
        self.size = 25
        self.health = 100
        self.max_health = 100
        self.alive = True
        self.angle = 0  # Tank body angle
        self.turret_angle = 0  # Turret angle
        self.speed = 4  # Increased speed for better responsiveness
        self.rotation_speed = 0.08  # Increased rotation speed
        self.controls = {}
        self.last_shot_time = 0
        self.shot_cooldown = 400  # milliseconds - reduced for more responsive shooting

        # Movement physics
        self.velocity_x = 0
        self.velocity_y = 0
        self.friction = 0.85  # Friction to slow down movement
        self.acceleration = 0.8

        # Tank dimensions
        self.width = 40
        self.height = 30
        self.turret_length = 35

    def update(self, bullets):
        if not self.alive:
            return

        keys = pygame.key.get_pressed()

        # Tank body movement and rotation
        if keys[self.controls['forward']]:
            self.accelerate_forward()
        if keys[self.controls['backward']]:
            self.accelerate_backward()
        if keys[self.controls['turn_left']]:
            self.rotate_tank_left()
        if keys[self.controls['turn_right']]:
            self.rotate_tank_right()

        # Independent turret rotation
        if keys[self.controls['turret_left']]:
            self.rotate_turret_left()
        if keys[self.controls['turret_right']]:
            self.rotate_turret_right()

        # Apply physics
        self.apply_physics()

        # Shooting
        if keys[self.controls['shoot']]:
            self.shoot(bullets)

        # Keep tank within screen bounds
        self.x = max(self.size, min(SCREEN_WIDTH - self.size, self.x))
        self.y = max(self.size, min(SCREEN_HEIGHT - self.size, self.y))

    def accelerate_forward(self):
        self.velocity_x += math.cos(self.angle) * self.acceleration
        self.velocity_y += math.sin(self.angle) * self.acceleration

    def accelerate_backward(self):
        self.velocity_x -= math.cos(self.angle) * self.acceleration
        self.velocity_y -= math.sin(self.angle) * self.acceleration

    def apply_physics(self):
        # Apply velocity to position
        self.x += self.velocity_x
        self.y += self.velocity_y

        # Apply friction
        self.velocity_x *= self.friction
        self.velocity_y *= self.friction

        # Cap maximum speed
        max_speed = self.speed
        current_speed = math.sqrt(self.velocity_x**2 + self.velocity_y**2)
        if current_speed > max_speed:
            self.velocity_x = (self.velocity_x / current_speed) * max_speed
            self.velocity_y = (self.velocity_y / current_speed) * max_speed

    def rotate_tank_left(self):
        self.angle -= self.rotation_speed
        self.turret_angle -= self.rotation_speed  # Turret follows tank body

    def rotate_tank_right(self):
        self.angle += self.rotation_speed
        self.turret_angle += self.rotation_speed  # Turret follows tank body

    def rotate_turret_left(self):
        self.turret_angle -= self.rotation_speed

    def rotate_turret_right(self):
        self.turret_angle += self.rotation_speed

    def shoot(self, bullets):
        current_time = pygame.time.get_ticks()
        if current_time - self.last_shot_time > self.shot_cooldown:
            # Calculate bullet starting position (at the end of turret)
            bullet_x = self.x + math.cos(self.turret_angle) * self.turret_length
            bullet_y = self.y + math.sin(self.turret_angle) * self.turret_length

            bullet = Bullet(bullet_x, bullet_y, self.turret_angle, self)
            bullets.append(bullet)
            self.last_shot_time = current_time

    def draw(self, screen):
        if not self.alive:
            return

        # Draw tank body (rectangle)
        tank_points = self.get_rotated_rect_points(self.x, self.y, self.width, self.height, self.angle)
        pygame.draw.polygon(screen, self.color, tank_points)
        pygame.draw.polygon(screen, BLACK, tank_points, 2)

        # Draw turret
        turret_end_x = self.x + math.cos(self.turret_angle) * self.turret_length
        turret_end_y = self.y + math.sin(self.turret_angle) * self.turret_length
        pygame.draw.line(screen, self.color, (self.x, self.y), (turret_end_x, turret_end_y), 8)
        pygame.draw.line(screen, BLACK, (self.x, self.y), (turret_end_x, turret_end_y), 2)

        # Draw tank center
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), 8)
        pygame.draw.circle(screen, BLACK, (int(self.x), int(self.y)), 8, 2)

    def get_rotated_rect_points(self, cx, cy, width, height, angle):
        """Get the four corner points of a rotated rectangle"""
        # Half dimensions
        hw, hh = width / 2, height / 2

        # Corner points relative to center
        corners = [(-hw, -hh), (hw, -hh), (hw, hh), (-hw, hh)]

        # Rotate and translate points
        rotated_points = []
        for x, y in corners:
            # Rotate
            rx = x * math.cos(angle) - y * math.sin(angle)
            ry = x * math.sin(angle) + y * math.cos(angle)
            # Translate
            rotated_points.append((cx + rx, cy + ry))

        return rotated_points

    def take_damage(self, damage):
        self.health -= damage
        if self.health <= 0:
            self.alive = False

class Bullet:
    def __init__(self, x, y, angle, owner):
        self.x = x
        self.y = y
        self.angle = angle
        self.owner = owner
        self.speed = 10
        self.damage = 25
        self.size = 4
        self.trail = []  # For bullet trail effect
        self.max_trail_length = 8

    def update(self):
        # Add current position to trail
        self.trail.append((self.x, self.y))
        if len(self.trail) > self.max_trail_length:
            self.trail.pop(0)

        # Move bullet
        self.x += math.cos(self.angle) * self.speed
        self.y += math.sin(self.angle) * self.speed

    def draw(self, screen):
        # Draw trail
        for i, (trail_x, trail_y) in enumerate(self.trail):
            alpha = int(255 * (i + 1) / len(self.trail))
            trail_size = int(self.size * (i + 1) / len(self.trail))
            if trail_size > 0:
                # Create a surface for alpha blending
                trail_surface = pygame.Surface((trail_size * 2, trail_size * 2))
                trail_surface.set_alpha(alpha // 2)
                trail_surface.fill(ORANGE)
                screen.blit(trail_surface, (trail_x - trail_size, trail_y - trail_size))

        # Draw main bullet
        pygame.draw.circle(screen, YELLOW, (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, ORANGE, (int(self.x), int(self.y)), self.size - 1)

class Explosion:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.particles = []
        self.finished = False
        self.timer = 0
        self.max_time = 30  # frames

        # Create explosion particles
        for _ in range(15):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(2, 8)
            particle = {
                'x': x,
                'y': y,
                'vx': math.cos(angle) * speed,
                'vy': math.sin(angle) * speed,
                'life': random.randint(15, 25),
                'max_life': random.randint(15, 25),
                'size': random.randint(3, 8)
            }
            self.particles.append(particle)

    def update(self):
        self.timer += 1
        if self.timer > self.max_time:
            self.finished = True
            return

        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['vx'] *= 0.95  # Friction
            particle['vy'] *= 0.95
            particle['life'] -= 1

            if particle['life'] <= 0:
                self.particles.remove(particle)

        if not self.particles:
            self.finished = True

    def draw(self, screen):
        for particle in self.particles:
            # Calculate alpha based on remaining life
            alpha = int(255 * (particle['life'] / particle['max_life']))
            size = int(particle['size'] * (particle['life'] / particle['max_life']))

            if size > 0:
                # Create colors that fade from yellow to red to black
                life_ratio = particle['life'] / particle['max_life']
                if life_ratio > 0.7:
                    color = YELLOW
                elif life_ratio > 0.3:
                    color = ORANGE
                else:
                    color = RED

                pygame.draw.circle(screen, color, (int(particle['x']), int(particle['y'])), size)

if __name__ == "__main__":
    game = Game()
    game.run()
